<template>
  <div class="h-full bg-bac p-5">
    <div v-loading="loading" class="data-list rounded-md">
      <div class="flex items-center px-5 py-4 pt-5">
        <h2 class="mr-5 text-2xl">数据集</h2>

        <!-- 搜索框 -->
        <div class="max-w-md flex-1">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据集名称或课题编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            @clear="handleSearch"
          />
        </div>

        <!-- 筛选器 -->
        <div class="ml-4 flex items-center space-x-3">
          <el-select v-model="filters.state" placeholder="状态" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option label="启用" value="启用" />
            <el-option label="禁用" value="禁用" />
            <el-option label="未发布" value="未发布" />
            <el-option label="废弃" value="废弃" />
          </el-select>

          <el-select v-model="filters.diseaseType" placeholder="病种" clearable @change="handleFilterChange">
            <el-option label="全部" value="" />
            <el-option label="肿瘤" value="肿瘤" />
            <el-option label="心血管" value="心血管" />
            <el-option label="神经系统" value="神经系统" />
            <el-option label="其他" value="其他" />
          </el-select>
        </div>
      </div>

      <div class="h-0 flex flex-1 flex-col">
        <div class="h-0 flex-1 px-5">
          <el-table
            ref="multipleTableRef"
            class="c-table-header h-full"
            :data="tableData"
            style="width: 100%"
            highlight-current-row
          >
            <el-table-column prop="datasetName" label="数据集名称" min-width="200px">
              <template #default="scope">
                <a class="cursor-pointer text-p" @click="handleRowClick(scope.row)">
                  {{ scope.row.datasetName || scope.row.datasetNameCn }}
                </a>
              </template>
            </el-table-column>

            <el-table-column prop="projectCode" label="课题编码" min-width="120px" />

            <el-table-column prop="description" label="描述" min-width="200px" show-overflow-tooltip />

            <el-table-column prop="diseaseType" label="病种" min-width="100px" />

            <el-table-column prop="dataManager" label="数据负责人" min-width="120px" />

            <el-table-column prop="createDate" label="创建时间" min-width="120px" />

            <el-table-column prop="state" label="状态" min-width="100px">
              <template #default="scope">
                <el-tag :type="getStateTagType(scope.row.state)" size="small">
                  {{ scope.row.state }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120px" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleViewDetail(scope.row)"> 查看详情 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-bottom">
          <el-pagination
            background
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 右侧详情面板 -->
    <div class="data-detail" :class="{ show: selectedDataset }" ref="dataDetailRef" @click.stop>
      <template v-if="selectedDataset">
        <div class="detail-header">
          <h3 class="text-xl font-bold">{{ selectedDataset.datasetName || selectedDataset.datasetNameCn }}</h3>
          <el-button type="primary" link @click="closeDetail">关闭</el-button>
        </div>

        <div class="detail-content">
          <!-- 基本信息 -->
          <div class="info-section">
            <h4 class="section-title">基本信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">课题编码：</span>
                <span class="value">{{ selectedDataset.projectCode }}</span>
              </div>
              <div class="info-item">
                <span class="label">病种：</span>
                <span class="value">{{ selectedDataset.diseaseType }}</span>
              </div>
              <div class="info-item">
                <span class="label">数据负责人：</span>
                <span class="value">{{ selectedDataset.dataManager }}</span>
              </div>
              <div class="info-item">
                <span class="label">所属单位：</span>
                <span class="value">{{ selectedDataset.affiliatedUnit }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ selectedDataset.createDate }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态：</span>
                <el-tag :type="getStateTagType(selectedDataset.state)" size="small">
                  {{ selectedDataset.state }}
                </el-tag>
              </div>
            </div>

            <div class="info-item full-width">
              <span class="label">描述：</span>
              <span class="value">{{ selectedDataset.description }}</span>
            </div>
          </div>

          <!-- 数据表列表 -->
          <div class="info-section">
            <h4 class="section-title">数据表</h4>
            <div v-loading="tablesLoading" class="tables-container">
              <div v-if="datasetTables.length === 0" class="empty-state">暂无数据表</div>
              <div v-else class="tables-list">
                <div v-for="table in datasetTables" :key="table.id" class="table-item" @click="handleTableClick(table)">
                  <div class="table-header">
                    <h5 class="table-name">{{ table.tableChineseName || table.tableName }}</h5>
                    <el-tag size="small">{{ table.fieldCount || 0 }} 个字段</el-tag>
                  </div>
                  <div class="table-info">
                    <span class="table-desc">{{ table.description || '暂无描述' }}</span>
                    <span class="table-rows">{{ table.tableRows || 0 }} 行数据</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 数据表详情对话框 -->
    <el-dialog
      v-model="tableDialogVisible"
      :title="selectedTable?.tableChineseName || selectedTable?.tableName"
      width="80%"
      top="5vh"
    >
      <div v-loading="fieldsLoading" class="table-detail-content">
        <!-- 表基本信息 -->
        <div class="table-basic-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">表名：</span>
              <span class="value">{{ selectedTable?.tableName }}</span>
            </div>
            <div class="info-item">
              <span class="label">中文名：</span>
              <span class="value">{{ selectedTable?.tableChineseName }}</span>
            </div>
            <div class="info-item">
              <span class="label">字段数量：</span>
              <span class="value">{{ selectedTable?.fieldCount || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="label">数据行数：</span>
              <span class="value">{{ selectedTable?.tableRows || 0 }}</span>
            </div>
          </div>
          <div class="info-item full-width">
            <span class="label">描述：</span>
            <span class="value">{{ selectedTable?.description || '暂无描述' }}</span>
          </div>
        </div>

        <!-- 字段列表 -->
        <div class="fields-section">
          <h4 class="section-title">字段信息</h4>
          <el-table :data="tableFields" style="width: 100%" max-height="400">
            <el-table-column prop="name" label="字段名" min-width="150" />
            <el-table-column prop="chineseMeaning" label="中文含义" min-width="150" />
            <el-table-column prop="dataType" label="数据类型" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="state" label="状态" width="80">
              <template #default="scope">
                <el-tag :type="getStateTagType(scope.row.state)" size="small">
                  {{ scope.row.state }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { Search } from '@element-plus/icons-vue';
  import { ref, reactive, onBeforeMount, watch } from 'vue';
  import { ElMessage } from 'element-plus';
  import { onClickOutside } from '@vueuse/core';
  import { findFileInforByUserId, findFileInforByCriteria } from '@/api/modules/sjzyxtzyxsjjwjglmk';
  import { findAllTableVOByDbId, findAllFieldVOByDbIdTblId } from '@/api/modules/sjzyxtzsjyglmk';

  // 类型定义
  interface Dataset {
    id: number;
    datasetName?: string;
    datasetNameCn?: string;
    projectCode?: string;
    description?: string;
    diseaseType?: string;
    dataManager?: string;
    affiliatedUnit?: string;
    createDate?: string;
    state?: string;
    parentId?: number;
  }

  interface DataTable {
    id: number;
    tableName?: string;
    tableChineseName?: string;
    description?: string;
    tableRows?: number;
    fieldCount?: number;
    dataBaseId?: number;
    state?: string;
  }

  interface DataField {
    id: number;
    name?: string;
    chineseMeaning?: string;
    dataType?: string;
    unit?: string;
    description?: string;
    state?: string;
    tableId?: number;
  }

  // 响应式数据
  const loading = ref(false);
  const tablesLoading = ref(false);
  const fieldsLoading = ref(false);
  const tableData = ref<Dataset[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const searchKeyword = ref('');

  // 筛选条件
  const filters = reactive({
    state: '',
    diseaseType: '',
  });

  // 详情面板相关
  const selectedDataset = ref<Dataset | null>(null);
  const dataDetailRef = ref<HTMLElement | null>(null);
  const datasetTables = ref<DataTable[]>([]);

  // 表详情对话框相关
  const tableDialogVisible = ref(false);
  const selectedTable = ref<DataTable | null>(null);
  const tableFields = ref<DataField[]>([]);

  // 获取当前用户ID（模拟）
  const currentUserId = ref(1);

  // 方法
  const getStateTagType = (state?: string): 'success' | 'warning' | 'info' | 'danger' | undefined => {
    switch (state) {
      case '启用':
        return 'success';
      case '禁用':
        return 'danger';
      case '未发布':
        return 'warning';
      case '废弃':
        return 'info';
      default:
        return undefined;
    }
  };

  const handleSearch = () => {
    currentPage.value = 1;
    fetchData();
  };

  const handleFilterChange = () => {
    currentPage.value = 1;
    fetchData();
  };

  const handleSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1;
    fetchData();
  };

  const handleCurrentChange = (page: number) => {
    currentPage.value = page;
    fetchData();
  };

  const handleRowClick = (row: Dataset) => {
    selectedDataset.value = row;
    loadDatasetTables(row);
  };

  const handleViewDetail = (row: Dataset) => {
    selectedDataset.value = row;
    loadDatasetTables(row);
  };

  const closeDetail = () => {
    selectedDataset.value = null;
    datasetTables.value = [];
  };

  const handleTableClick = (table: DataTable) => {
    selectedTable.value = table;
    tableDialogVisible.value = true;
    loadTableFields(table);
  };

  // 获取数据集列表
  async function fetchData() {
    try {
      loading.value = true;

      // 构建查询条件
      const criteria: any = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      };

      // 添加搜索关键词
      if (searchKeyword.value) {
        criteria.datasetName = searchKeyword.value;
        criteria.projectCode = searchKeyword.value;
      }

      // 添加筛选条件
      if (filters.state) {
        criteria.state = filters.state;
      }
      if (filters.diseaseType) {
        criteria.diseaseType = filters.diseaseType;
      }

      // 调用API获取数据
      let response;
      if (Object.keys(criteria).length > 2) {
        // 有筛选条件时使用条件查询
        response = await findFileInforByCriteria(criteria);
      } else {
        // 无筛选条件时使用用户ID查询
        response = await findFileInforByUserId(currentUserId.value, currentPage.value, pageSize.value);
      }

      if (response.code === 200 && response.data) {
        const pageData = response.data;
        // 转换API数据到我们的Dataset类型
        const datasets: Dataset[] = (pageData.content || []).map((item: any) => ({
          id: item.id || 0,
          datasetName: item.datasetName,
          datasetNameCn: item.datasetNameCn,
          projectCode: item.projectCode,
          description: item.description,
          diseaseType: item.diseaseType,
          dataManager: item.dataManager,
          affiliatedUnit: item.affiliatedUnit,
          createDate: item.createDate,
          state: item.state,
          parentId: item.parentId,
        }));
        tableData.value = datasets;
        total.value = pageData.totalElement || 0;
      } else {
        // 如果API调用失败，使用模拟数据
        await loadMockData();
      }
    } catch (error) {
      console.error('获取数据集列表失败:', error);
      // 使用模拟数据作为后备
      await loadMockData();
    } finally {
      loading.value = false;
    }
  }

  // 模拟数据（用于开发和测试）
  async function loadMockData() {
    await new Promise((resolve) => setTimeout(resolve, 300));

    const mockData: Dataset[] = [
      {
        id: 1,
        datasetName: 'TCGA-BRCA-Dataset',
        datasetNameCn: '乳腺癌基因组数据集',
        projectCode: 'PROJ001',
        description: '包含乳腺癌患者的基因组测序数据，用于癌症研究和治疗方案开发',
        diseaseType: '肿瘤',
        dataManager: '张医生',
        affiliatedUnit: '肿瘤医院',
        createDate: '2024-01-15',
        state: '启用',
        parentId: 1,
      },
      {
        id: 2,
        datasetName: 'TCGA-LUAD-Dataset',
        datasetNameCn: '肺腺癌基因组数据集',
        projectCode: 'PROJ002',
        description: '肺腺癌患者的全基因组测序和转录组数据',
        diseaseType: '肿瘤',
        dataManager: '李医生',
        affiliatedUnit: '胸科医院',
        createDate: '2024-01-20',
        state: '启用',
        parentId: 1,
      },
      {
        id: 3,
        datasetName: 'Heart-Disease-Clinical',
        datasetNameCn: '心脏病临床数据集',
        projectCode: 'PROJ003',
        description: '心血管疾病患者的临床检查数据和影像学资料',
        diseaseType: '心血管',
        dataManager: '王医生',
        affiliatedUnit: '心血管医院',
        createDate: '2024-02-01',
        state: '启用',
        parentId: 2,
      },
      {
        id: 4,
        datasetName: 'Neuro-Imaging-Study',
        datasetNameCn: '神经影像学研究数据',
        projectCode: 'PROJ004',
        description: '神经系统疾病的MRI和CT影像数据集',
        diseaseType: '神经系统',
        dataManager: '赵医生',
        affiliatedUnit: '神经医院',
        createDate: '2024-02-10',
        state: '未发布',
        parentId: 3,
      },
      {
        id: 5,
        datasetName: 'COVID-RNA-Seq',
        datasetNameCn: 'COVID-19 RNA测序数据',
        projectCode: 'PROJ005',
        description: 'COVID-19患者的RNA测序数据和免疫反应分析',
        diseaseType: '其他',
        dataManager: '陈医生',
        affiliatedUnit: '传染病医院',
        createDate: '2024-02-15',
        state: '启用',
        parentId: 1,
      },
    ];

    // 应用筛选
    let filteredData = mockData;

    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.datasetName?.toLowerCase().includes(keyword) ||
          item.datasetNameCn?.toLowerCase().includes(keyword) ||
          item.projectCode?.toLowerCase().includes(keyword)
      );
    }

    if (filters.state) {
      filteredData = filteredData.filter((item) => item.state === filters.state);
    }

    if (filters.diseaseType) {
      filteredData = filteredData.filter((item) => item.diseaseType === filters.diseaseType);
    }

    total.value = filteredData.length;

    // 分页
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    tableData.value = filteredData.slice(start, end);
  }

  // 加载数据集的数据表
  async function loadDatasetTables(dataset: Dataset) {
    try {
      tablesLoading.value = true;
      datasetTables.value = [];

      if (!dataset.parentId) {
        console.warn('数据集缺少parentId，无法加载数据表');
        return;
      }

      // 调用API获取数据表列表
      const response = await findAllTableVOByDbId(dataset.parentId);

      if (response.code === 200 && response.data) {
        // 转换API数据到我们的DataTable类型
        const tables: DataTable[] = (response.data || []).map((item: any) => ({
          id: item.id || 0,
          tableName: item.tableName,
          tableChineseName: item.tableChineseName,
          description: item.description,
          tableRows: item.tableRows,
          fieldCount: item.fieldCount,
          dataBaseId: item.dataBaseId,
          state: item.state,
        }));
        datasetTables.value = tables;
      } else {
        // 使用模拟数据
        await loadMockTables(dataset.id);
      }
    } catch (error) {
      console.error('获取数据表列表失败:', error);
      // 使用模拟数据作为后备
      await loadMockTables(dataset.id);
    } finally {
      tablesLoading.value = false;
    }
  }

  // 模拟数据表数据
  async function loadMockTables(datasetId: number) {
    await new Promise((resolve) => setTimeout(resolve, 200));

    const mockTables: Record<number, DataTable[]> = {
      1: [
        {
          id: 1,
          tableName: 'patient_info',
          tableChineseName: '患者基本信息表',
          description: '包含患者的基本信息，如年龄、性别、诊断等',
          tableRows: 1250,
          fieldCount: 15,
          dataBaseId: 1,
          state: '启用',
        },
        {
          id: 2,
          tableName: 'gene_expression',
          tableChineseName: '基因表达数据表',
          description: '基因表达量数据，包含各种基因的表达水平',
          tableRows: 50000,
          fieldCount: 8,
          dataBaseId: 1,
          state: '启用',
        },
        {
          id: 3,
          tableName: 'mutation_data',
          tableChineseName: '突变数据表',
          description: '基因突变信息，包含突变类型和位置',
          tableRows: 8500,
          fieldCount: 12,
          dataBaseId: 1,
          state: '启用',
        },
      ],
      2: [
        {
          id: 4,
          tableName: 'clinical_data',
          tableChineseName: '临床数据表',
          description: '临床检查结果和诊断信息',
          tableRows: 2100,
          fieldCount: 20,
          dataBaseId: 2,
          state: '启用',
        },
        {
          id: 5,
          tableName: 'lab_results',
          tableChineseName: '实验室检查结果',
          description: '血液检查、生化指标等实验室数据',
          tableRows: 5600,
          fieldCount: 25,
          dataBaseId: 2,
          state: '启用',
        },
      ],
      3: [
        {
          id: 6,
          tableName: 'ecg_data',
          tableChineseName: '心电图数据',
          description: '心电图检查数据和分析结果',
          tableRows: 1800,
          fieldCount: 18,
          dataBaseId: 3,
          state: '启用',
        },
      ],
      4: [
        {
          id: 7,
          tableName: 'mri_images',
          tableChineseName: 'MRI影像数据',
          description: 'MRI扫描图像和相关参数',
          tableRows: 950,
          fieldCount: 10,
          dataBaseId: 4,
          state: '未发布',
        },
      ],
      5: [
        {
          id: 8,
          tableName: 'rna_seq_data',
          tableChineseName: 'RNA测序数据',
          description: 'RNA测序原始数据和处理结果',
          tableRows: 12000,
          fieldCount: 14,
          dataBaseId: 5,
          state: '启用',
        },
      ],
    };

    datasetTables.value = mockTables[datasetId] || [];
  }

  // 加载数据表的字段信息
  async function loadTableFields(table: DataTable) {
    try {
      fieldsLoading.value = true;
      tableFields.value = [];

      if (!table.dataBaseId || !table.id) {
        console.warn('数据表缺少必要信息，无法加载字段');
        return;
      }

      // 调用API获取字段列表
      const response = await findAllFieldVOByDbIdTblId(table.dataBaseId, table.id);

      if (response.code === 200 && response.data) {
        // 转换API数据到我们的DataField类型
        const fields: DataField[] = (response.data || []).map((item: any) => ({
          id: item.id || 0,
          name: item.name,
          chineseMeaning: item.chineseMeaning,
          dataType: item.dataType,
          unit: item.unit,
          description: item.description,
          state: item.state,
          tableId: item.tableId,
        }));
        tableFields.value = fields;
      } else {
        // 使用模拟数据
        await loadMockFields(table.id);
      }
    } catch (error) {
      console.error('获取字段列表失败:', error);
      // 使用模拟数据作为后备
      await loadMockFields(table.id);
    } finally {
      fieldsLoading.value = false;
    }
  }

  // 模拟字段数据
  async function loadMockFields(tableId: number) {
    await new Promise((resolve) => setTimeout(resolve, 200));

    const mockFields: Record<number, DataField[]> = {
      1: [
        {
          id: 1,
          name: 'patient_id',
          chineseMeaning: '患者ID',
          dataType: 'S',
          unit: '',
          description: '患者唯一标识符',
          state: '启用',
          tableId: 1,
        },
        {
          id: 2,
          name: 'age',
          chineseMeaning: '年龄',
          dataType: 'N',
          unit: '岁',
          description: '患者年龄',
          state: '启用',
          tableId: 1,
        },
        {
          id: 3,
          name: 'gender',
          chineseMeaning: '性别',
          dataType: 'E',
          unit: '',
          description: '患者性别，M=男性，F=女性',
          state: '启用',
          tableId: 1,
        },
        {
          id: 4,
          name: 'diagnosis',
          chineseMeaning: '诊断',
          dataType: 'S',
          unit: '',
          description: '主要诊断信息',
          state: '启用',
          tableId: 1,
        },
      ],
      2: [
        {
          id: 5,
          name: 'gene_symbol',
          chineseMeaning: '基因符号',
          dataType: 'S',
          unit: '',
          description: '基因的标准符号',
          state: '启用',
          tableId: 2,
        },
        {
          id: 6,
          name: 'expression_level',
          chineseMeaning: '表达水平',
          dataType: 'F',
          unit: 'FPKM',
          description: '基因表达水平数值',
          state: '启用',
          tableId: 2,
        },
      ],
      // 可以继续添加其他表的字段...
    };

    tableFields.value = mockFields[tableId] || [];
  }

  // 点击外部关闭详情面板
  onClickOutside(dataDetailRef, () => {
    if (selectedDataset.value) {
      closeDetail();
    }
  });

  // 监听筛选条件变化
  watch(
    [() => filters.state, () => filters.diseaseType],
    () => {
      handleFilterChange();
    },
    { deep: true }
  );

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });
</script>

<style scoped>
  .data-list {
    @apply bg-white h-full flex flex-col;
  }

  .pagination-bottom {
    @apply flex justify-center py-4;
  }

  /* 详情面板样式 */
  .data-detail {
    @apply fixed top-0 right-0 h-full w-96 bg-white shadow-lg transform translate-x-full transition-transform duration-300 z-50;
  }

  .data-detail.show {
    @apply translate-x-0;
  }

  .detail-header {
    @apply flex items-center justify-between p-4 border-b border-gray-200;
  }

  .detail-content {
    @apply p-4 h-0 flex-1 overflow-y-auto;
  }

  .info-section {
    @apply mb-6;
  }

  .section-title {
    @apply text-lg font-semibold mb-3 text-gray-800;
  }

  .info-grid {
    @apply grid grid-cols-1 gap-3;
  }

  .info-item {
    @apply flex items-start;
  }

  .info-item.full-width {
    @apply col-span-full flex-col items-start;
  }

  .info-item .label {
    @apply text-sm text-gray-600 min-w-20 mr-2;
  }

  .info-item .value {
    @apply text-sm text-gray-900 flex-1;
  }

  .info-item.full-width .label {
    @apply mb-1;
  }

  /* 数据表列表样式 */
  .tables-container {
    @apply min-h-32;
  }

  .empty-state {
    @apply text-center text-gray-500 py-8;
  }

  .tables-list {
    @apply space-y-3;
  }

  .table-item {
    @apply p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors;
  }

  .table-header {
    @apply flex items-center justify-between mb-2;
  }

  .table-name {
    @apply text-sm font-medium text-gray-900;
  }

  .table-info {
    @apply flex items-center justify-between text-xs text-gray-600;
  }

  .table-desc {
    @apply flex-1 mr-2;
  }

  .table-rows {
    @apply text-blue-600;
  }

  /* 表详情对话框样式 */
  .table-detail-content {
    @apply space-y-4;
  }

  .table-basic-info {
    @apply p-4 bg-gray-50 rounded-lg;
  }

  .fields-section {
    @apply space-y-3;
  }

  /* 响应式调整 */
  @media (max-width: 1024px) {
    .data-detail {
      @apply w-80;
    }
  }

  @media (max-width: 768px) {
    .data-detail {
      @apply w-full;
    }

    .info-grid {
      @apply grid-cols-1;
    }
  }
</style>
